<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费账单管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>缴费状态：</label>
                                <select name="payStatus">
                                    <option value="">所有</option>
                                    <option value="0">未缴</option>
                                    <option value="1">已缴</option>
                                    <option value="2">部分缴费</option>
                                </select>
                            </li>
                            <li>
                                <label>账期范围：</label>
                                <input type="text" id="billPeriodRange" name="billPeriodRange" placeholder="选择账期范围" class="form-control" style="width: 200px;" readonly/>
                                <input type="hidden" name="startMonth" id="startMonth"/>
                                <input type="hidden" name="endMonth" id="endMonth"/>
                            </li>
                            <li>
                                <label>收费标准：</label>
                                <select name="chargeStandardId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>资产类型：</label>
                                <select name="assetType" th:with="type=${@dict.getType('asset_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>资产名称：</label>
                                <input type="text" name="assetName" placeholder="请输入资产名称"/>
                            </li>
                            <li>
                                <label>收费项目：</label>
                                <input type="text" name="chargeItemName" placeholder="请输入收费项目名称"/>
                            </li>
                            <li>
                                <label>账单类型：</label>
                                <select name="billType">
                                    <option value="">所有</option>
                                    <option value="1">手工账单</option>
                                    <option value="2">补缴账单</option>
                                    <option value="3">系统生成</option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" name="beginTime" placeholder="开始时间" class="form-control" style="width: 120px;"/>
                            </li>
                            <li>
                                <input type="text" name="endTime" placeholder="结束时间" class="form-control" style="width: 120px;"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="oc:charge:bill:add">
                    <i class="fa fa-plus"></i> 手动创建账单
                </a>
                <a class="btn btn-primary" onclick="batchGenerate()" shiro:hasPermission="oc:charge:bill:generate">
                    <i class="fa fa-magic"></i> 预生成账单
                </a>
                <a class="btn btn-success" onclick="batchPayment()" shiro:hasPermission="oc:charge:bill:payment">
                    <i class="fa fa-money"></i> 批量收款
                </a>
                <a class="btn btn-info" onclick="showStatistics()" shiro:hasPermission="oc:charge:bill:statistics">
                    <i class="fa fa-bar-chart"></i> 统计信息
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="oc:charge:bill:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = true;
        var removeFlag = true;
        var generateFlag = true;
        var prefix = ctx + "oc/charge/manage/bill";



        $(function() {
            // 初始化账期范围选择器
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#billPeriodRange',
                    type: 'month',
                    range: '至',
                    format: 'yyyy-MM',
                    done: function(value, date, endDate){
                        if (value) {
                            var dates = value.split(' 至 ');
                            if (dates.length === 2) {
                                $('#startMonth').val(dates[0]);
                                $('#endMonth').val(dates[1]);
                            }
                        } else {
                            $('#startMonth').val('');
                            $('#endMonth').val('');
                        }
                    }
                });
            });

            // 加载收费标准下拉选项
            $.post(prefix + "/getChargeStandards", {}, function(result) {
                if (result.code == 0) {
                    var select = $("select[name='chargeStandardId']");
                    select.empty();
                    select.append('<option value="">所有</option>');
                    $.each(result.data, function(index, item) {
                        select.append('<option value="' + item.id + '">' + item.name + '</option>');
                    });
                }
            });


            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "收费账单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '账单ID',
                    sortable: true,
                    visible: false,
                    width: 100
                },{
                    field: 'pay_status_str',
                    title: '缴费状态',
                    width: 100,
                    formatter: function(value, row, index) {
                        var payStatus = row.pay_status || 0;
                        var isBadBill = row.is_bad_bill || 0;
                        var statusText = value || '未缴';

                        // 作废状态优先显示
                        if (isBadBill == 1) {
                            return '<span class="label label-default">' + statusText + '</span>';
                        } else if (payStatus == 0) {
                            return '<span class="label label-danger">' + statusText + '</span>';
                        } else if (payStatus == 1) {
                            return '<span class="label label-success">' + statusText + '</span>';
                        } else {
                            return '<span class="label label-warning">' + statusText + '</span>';
                        }
                    }
                },{
                    field: 'charge_standard_name',
                    title: '收费标准',
                    width: 150
                },
                {
                    field: 'bill_type_str',
                    title: '账单类型',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '系统生成';
                    }
                },
                {
                    field: 'asset_type_str',
                    title: '资产类型',
                    width: 80
                },
                {
                    field: 'asset_name',
                    title: '资产名称',
                    sortable: true,
                    width: 200
                },
                {
                    field: 'ownerInfoStr',
                    title: '绑定住户',
                    sortable: true,
                    width: 150
                },
                {
                    field: 'charge_item_name',
                    title: '收费项目',
                    sortable: true,
                    width: 150
                },
                {
                    field: 'charge_item_type_str',
                    title: '收费类型',
                    width: 100
                },
                {
                    field: 'start_time',
                    title: '计费开始日期',
                    width: 180,
                    formatter: function(value, row, index) {
                        return value +' / '+row.end_time;
                    }
                },
                {
                    field: 'in_month',
                    title: '账期月份',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'bill_amount_yuan',
                    title: '应缴费金额',
                    sortable: true,
                    width: 120
                },
                {
                    field: 'discount_amount_yuan',
                    title: '优惠',
                    width: 120
                },
                {
                    field: 'late_money_amount_yuan',
                    title: '违约金',
                    width: 100
                },
                {
                    field: 'pay_type_str',
                    title: '支付方式',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'pay_time_str',
                    title: '缴费时间',
                    width: 150,
                    formatter: function(value, row, index) {
                        return value || '';
                    }
                },{
                    field: 'remark',
                    title: '备注',
                    width: 150
                },
                {
                    field: 'create_time_str',
                    title: '账单生成时间',
                    sortable: true,
                    width: 150,
                    formatter: function(value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'last_op_time',
                    title: '最后更新时间',
                    width: 150,
                    formatter: function(value, row, index) {
                        if (value && (typeof value === 'string' || typeof value === 'number')) {
                            try {
                                return new Date(value).toLocaleString();
                            } catch (e) {
                                return '';
                            }
                        }
                        return '';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // 只有未缴费的账单才能作废
                        if (row.pay_status == 0) {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="voidBill(\'' + row.id + '\')"><i class="fa fa-ban"></i>作废</a> ');
                        }
                        if (removeFlag && row.payStatus == 0) {
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 批量生成账单
        function batchGenerate() {
            // 使用layer弹窗（如果系统中有layer）或者简单的prompt
            layer.open({
                type: 1,
                title: '选择生成账单的时间范围',
                area: ['500px', '300px'],
                content: '<div style="padding: 20px;">' +
                    '<div class="form-group">' +
                    '<label>开始月份：</label>' +
                    '<input type="text" id="billStartMonth" class="form-control" value="2025-01" style="margin-bottom: 15px;">' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label>结束月份：</label>' +
                    '<input type="text" id="billEndMonth" class="form-control" value="2025-06">' +
                    '</div>' +
                    '</div>',
                btn: ['确定', '取消'],
                success: function(index) {
                    layui.use('laydate', function() {
                        debugger;
                        var laydate = layui.laydate;
                        laydate.render({
                            elem: '#billStartMonth',
                            type: 'month',
                            format: 'yyyy-MM'
                        });
                        laydate.render({
                            elem: '#billEndMonth',
                            type: 'month',
                            format: 'yyyy-MM'
                        });
                    });
                },
                yes: function(index) {
                    var startMonth = $("#billStartMonth").val();
                    var endMonth = $("#billEndMonth").val();

                    if (!startMonth || !endMonth) {
                        layer.msg("请选择时间范围");
                        return;
                    }

                    if (startMonth > endMonth) {
                        layer.msg("开始月份不能大于结束月份");
                        return;
                    }

                    layer.close(index);
                    layer.msg("正在生成账单，请稍候...", {icon: 16, shade: 0.3, time: 0});

                    $.post(prefix + "/batchGenerate", {
                        startMonth: startMonth,
                        endMonth: endMonth
                    }, function(result) {
                        layer.closeAll('loading');
                        if (result.code == web_status.SUCCESS) {
                            layer.msg(result.msg, {icon: 1});
                            $("#bootstrap-table").bootstrapTable('refresh');
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    });
                }
            });
        }



        // 作废账单
        function voidBill(billId) {
            $.modal.confirm("确认要作废这条账单吗？", function() {
                var data = { "billId": billId };
                $.operate.submit(prefix + "/void", "POST", "json", data);
            });
        }

        // 批量收款
        function batchPayment() {
            var rows = $("#bootstrap-table").bootstrapTable('getSelections');
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条未缴费的账单");
                return;
            }

            // 检查选中的账单是否都是未缴费状态
            var unpaidBills = [];
            var assetIds = new Set();
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                if (row.pay_status != 0) {
                    $.modal.alertWarning("只能对未缴费的账单进行收款操作");
                    return;
                }
                if (row.is_bad_bill == 1) {
                    $.modal.alertWarning("已作废的账单不能收款");
                    return;
                }
                unpaidBills.push(row);
                assetIds.add(row.asset_id);
            }

            // 检查是否只选择了一个资产的账单
            if (assetIds.size > 1) {
                $.modal.alertWarning("批量收款只能选择同一个资产的账单");
                return;
            }

            // 计算总金额
            var totalAmount = 0;
            for (var i = 0; i < unpaidBills.length; i++) {
                totalAmount += parseFloat(unpaidBills[i].amount_yuan || 0);
            }

            // 显示收款确认对话框
            var assetName = unpaidBills[0].asset_name;
            var billIds = unpaidBills.map(function(bill) { return bill.id; });

            var content = '<div class="form-group" style="padding: 15px;">' +
                '<label>资产名称：</label><span>' + assetName + '</span><br>' +
                '<label>账单数量：</label><span>' + unpaidBills.length + ' 条</span><br>' +
                '<label>收款金额：</label><span style="color: red; font-weight: bold;">' + totalAmount.toFixed(2) + ' 元</span><br><br>' +
                '<label>支付方式：</label>' +
                '<select id="paymentType" class="form-control" style="width: 150px; display: inline-block;">' +
                '<option value="1">现金</option>' +
                '<option value="2">银行转账</option>' +
                '<option value="3">微信支付</option>' +
                '<option value="4">支付宝</option>' +
                '<option value="5">其他</option>' +
                '</select><br><br>' +
                '<label>备注：</label>' +
                '<textarea id="paymentRemark" class="form-control" style="width: 90%;height: 80px;" placeholder="请输入收款备注"></textarea>' +
                '</div>';

            layer.open({
                type: 1,
                title: '批量收款确认',
                content: content,
                area: ['500px', '400px'],
                btn: ['确认收款', '取消'],
                yes: function(index, layero) {
                    var paymentType = $('#paymentType').val();
                    var remark = $('#paymentRemark').val();

                    // 执行批量收款
                    $.ajax({
                        url: prefix + "/batchPayment",
                        type: "POST",
                        dataType: "json",
                        data: {
                            billIds: billIds.join(','),
                            paymentType: paymentType,
                            remark: remark
                        },
                        success: function(result) {
                            if (result.code == web_status.SUCCESS) {
                                $.modal.alertSuccess(result.msg);
                                $("#bootstrap-table").bootstrapTable('refresh');
                            } else {
                                $.modal.alertError(result.msg);
                            }
                        },
                        error: function() {
                            $.modal.alertError("批量收款失败，请稍后重试");
                        }
                    });

                    layer.close(index);
                }
            });
        }

        // 显示统计信息
        function showStatistics() {
            $.get(prefix + "/statistics", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var content = '<div class="row">' +
                        '<div class="col-sm-6"><p><strong>总账单数：</strong>' + data.totalCount + '</p></div>' +
                        '<div class="col-sm-6"><p><strong>未缴费账单：</strong>' + data.unpaidCount + '</p></div>' +
                        '<div class="col-sm-6"><p><strong>已缴费账单：</strong>' + data.paidCount + '</p></div>' +
                        '<div class="col-sm-6"><p><strong>总应收金额：</strong>' + data.totalAmount.toFixed(2) + ' 元</p></div>' +
                        '<div class="col-sm-6"><p><strong>已收金额：</strong>' + data.paidAmount.toFixed(2) + ' 元</p></div>' +
                        '<div class="col-sm-6"><p><strong>未收金额：</strong>' + data.unpaidAmount.toFixed(2) + ' 元</p></div>' +
                        '</div>';
                    $.modal.alertSuccess(content);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    </script>
</body>
</html>
