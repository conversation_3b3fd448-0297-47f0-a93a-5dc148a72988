package com.ehome.oc.service;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.constants.ChargeConstants;
import com.ehome.oc.domain.BillCreateRequest;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ChargeBillService {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBillService.class);

    @Autowired
    private ChargeBindingService chargeBindingService;

    @Autowired
    private BillPeriodCalculatorService billPeriodCalculatorService;



    /**
     * 通用的账单创建方法
     */
    private Long createBillRecord(BillCreateRequest request) {
        Record bill = new Record();

        // 设置基本信息
        bill.set("community_id", request.getCommunityId());
        bill.set("asset_type", request.getAssetType());
        bill.set("asset_id", request.getAssetId());
        bill.set("asset_name", request.getAssetName());
        bill.set("charge_standard_id", request.getChargeStandardId());
        bill.set("charge_standard_version", request.getChargeStandardVersion() != null ? request.getChargeStandardVersion() : 1);
        bill.set("charge_binding_id", request.getChargeBindingId() != null ? request.getChargeBindingId() : 0L);
        bill.set("charge_item_name", request.getChargeItemName());
        bill.set("charge_item_type", request.getChargeItemType() != null ? request.getChargeItemType() : 1);

        // 设置账期信息
        bill.set("start_time", request.getStartTime());
        bill.set("end_time", request.getEndTime());
        bill.set("in_month", request.getInMonth());
        bill.set("bill_type", request.getBillType() != null ? request.getBillType() : 3); // 默认系统生成

        // 设置金额信息（直接存储元，无需转换）
        BigDecimal baseAmount = request.getBillAmount() != null ? request.getBillAmount() : BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal lateMoneyAmount = BigDecimal.ZERO;

        // amount = 账单金额（应收金额）
        bill.set("amount", baseAmount);

        // bill_amount = 账单金额 + 违约金 - 优惠金额
        BigDecimal finalBillAmount = baseAmount.add(lateMoneyAmount).subtract(discountAmount);
        bill.set("bill_amount", finalBillAmount);

        bill.set("discount_amount", discountAmount);
        bill.set("late_money_amount", lateMoneyAmount);
        bill.set("deposit_amount", BigDecimal.ZERO);

        // 设置支付信息
        bill.set("pay_status", 0); // 未缴
        bill.set("pay_type", 0);
        bill.set("pay_time", 0);
        bill.set("second_pay_channel", 0);
        bill.set("second_pay_amount", BigDecimal.ZERO);

        // 设置其他信息
        bill.set("can_revoke", 0);
        bill.set("bad_bill_state", 0);
        bill.set("is_bad_bill", false);
        bill.set("has_split", false);
        bill.set("version", 0);
        bill.set("deal_log_id", 0L);

        // 设置审计字段（使用varchar(19)格式）
        String currentTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date());
        bill.set("create_time", currentTime);
        bill.set("create_by", request.getCurrentUser().getUserName());
        bill.set("update_time", currentTime);
        bill.set("update_by", request.getCurrentUser().getUserName());
        bill.set("last_op_time", currentTime);

        // 保存账单
        Db.save("eh_charge_bill", "id", bill);
        Long billId = bill.getLong("id");

        // 如果是房屋账单，更新房屋欠费金额
        if (request.getAssetType() != null && request.getAssetType() == 1) {
            updateHouseArrearAmount(request.getAssetId());
        }

        return billId;
    }

    /**
     * 获取账单详情
     */
    public Record getBillDetail(Long billId) {
        Record bill = Db.findFirst(
                "select cb.*, cs.name as charge_standard_name " +
                "from eh_charge_bill cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id and cb.charge_standard_version = cs.version " +
                "where cb.id = ?", billId);

        if (bill != null) {
            // 通过资产ID获取关联用户信息
            Integer assetType = bill.getInt("asset_type");
            Long assetId = bill.getLong("asset_id");

            if (assetType != null && assetId != null) {
                String ownerInfoStr = getAssetUsersString(assetType, assetId);
                bill.set("owneInfoStr", ownerInfoStr);
            }
        }

        return bill;
    }

    /**
     * 手动创建账单
     */
    public Long createManualBill(JSONObject params, SysUser currentUser) throws Exception {
        // 验证必要参数
        validateBillParams(params);

        // 获取收费标准版本号
        Record chargeStandard = Db.findFirst("select version from eh_charge_standard where id = ? and is_current = 1", params.getLongValue("chargeStandardId"));
        Integer version = (chargeStandard != null) ? chargeStandard.getInt("version") : 1;

        // 创建账单请求
        BillCreateRequest request = new BillCreateRequest();
        request.setCommunityId(currentUser.getCommunityId());
        request.setAssetType(params.getIntValue("assetType"));
        request.setAssetId(params.getLongValue("assetId"));
        request.setAssetName(params.getString("assetName"));
        request.setChargeStandardId(params.getLongValue("chargeStandardId"));
        request.setChargeStandardVersion(version);
        request.setChargeBindingId(params.getLong("chargeBindingId"));
        request.setChargeItemName(params.getString("chargeItemName"));
        request.setChargeItemType(params.getInteger("chargeItemType"));
        // 将时间戳转换为yyyyMMdd格式
        Long startTimeStamp = params.getLongValue("startTime");
        Long endTimeStamp = params.getLongValue("endTime");
        request.setStartTime(Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", new Date(startTimeStamp * 1000))));
        request.setEndTime(Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", new Date(endTimeStamp * 1000))));
        request.setInMonth(params.getString("inMonth"));
        request.setBillType(1); // 手工账单
        request.setBillAmount(new BigDecimal(params.getString("amount")));
        request.setCurrentUser(currentUser);

        // 创建账单记录
        Long billId = createBillRecord(request);

        // 手工账单特殊设置
        Db.update("update eh_charge_bill set can_revoke = 1, remark = ? where id = ?",
                params.getString("remark"), billId);

        logger.info("手动创建账单成功，账单ID：{}", billId);
        return billId;
    }

    /**
     * 更新账单
     */
    public boolean updateBill(JSONObject params, SysUser currentUser) throws Exception {
        Long billId = params.getLong("id");
        if (billId == null || billId <= 0) {
            throw new Exception("账单ID不能为空");
        }

        Record bill = Db.findFirst("select * from eh_charge_bill where id = ?", billId);
        if (bill == null) {
            throw new Exception("账单不存在");
        }

        // 更新账单信息
        bill.set("charge_item_name", params.getString("chargeItemName"));
        
        // 更新金额信息
        if (params.containsKey("amount")) {
            BigDecimal amount = new BigDecimal(params.getString("amount"));
            bill.set("amount", amount.multiply(new BigDecimal(100)).longValue());
            bill.set("bill_amount", amount.multiply(new BigDecimal(100)).longValue());
        }

        bill.set("remark", params.getString("remark"));
        bill.set("update_time", System.currentTimeMillis() / 1000);
        bill.set("update_by", currentUser.getUserName());
        bill.set("last_op_time", DateUtils.getTime());

        boolean result = Db.update("eh_charge_bill", "id", bill);
        
        if (result) {
            logger.info("更新账单成功，账单ID：{}", billId);
        }
        
        return result;
    }

    /**
     * 删除账单
     */
    public int deleteBills(String[] billIds, SysUser currentUser) throws Exception {
        int deleteCount = 0;
        
        for (String billIdStr : billIds) {
            Long billId = Long.parseLong(billIdStr);
            
            // 检查账单是否可以删除
            Record bill = Db.findFirst("select * from eh_charge_bill where id = ?", billId);
            if (bill == null) {
                continue;
            }
            Integer payStatus = bill.getInt("pay_status");
            if (payStatus != null && payStatus > 0) {
                throw new Exception("已缴费的账单不能删除");
            }
            // 获取房屋ID用于更新欠费金额
            Long assetId = bill.getInt("asset_type") == 1 ? bill.getLong("asset_id") : null;

            // 删除账单
            int result = Db.delete("delete from eh_charge_bill where id = ?", billId);
            if (result > 0) {
                deleteCount++;
                logger.info("删除账单成功，账单ID：{}", billId);

                // 如果是房屋账单，更新房屋欠费金额
                if (assetId != null) {
                    updateHouseArrearAmount(assetId);
                }
            }
        }
        return deleteCount;
    }

    /**
     * 作废账单
     */
    public boolean voidBill(Long billId, SysUser currentUser) throws Exception {
        if (billId == null || billId <= 0) {
            throw new Exception("账单ID不能为空");
        }

        Record bill = Db.findFirst("select * from eh_charge_bill where id = ?", billId);
        if (bill == null) {
            throw new Exception("账单不存在");
        }

        // 检查账单状态
        Integer payStatus = bill.getInt("pay_status");
        if (payStatus != null && payStatus > 0) {
            throw new Exception("已缴费的账单不能作废");
        }

        // 检查账单是否已经作废
        Integer isBadBill = bill.getInt("is_bad_bill");
        if (isBadBill != null && isBadBill == 1) {
            throw new Exception("账单已经是作废状态");
        }

        // 获取房屋ID用于更新欠费金额
        Long assetId = bill.getInt("asset_type") == 1 ? bill.getLong("asset_id") : null;

        // 更新账单为作废状态
        String currentTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date());
        int result = Db.update(
                "update eh_charge_bill set is_bad_bill = 1, bad_bill_state = 1, " +
                "update_time = ?, update_by = ?, remark = CONCAT(IFNULL(remark, ''), '\\n', ?) " +
                "where id = ?",
                currentTime, currentUser.getUserName(),
                "账单于 " + currentTime + " 被 " + currentUser.getUserName() + " 作废",
                billId);

        if (result > 0) {
            logger.info("作废账单成功，账单ID：{}，操作人：{}", billId, currentUser.getUserName());

            // 如果是房屋账单，更新房屋欠费金额
            if (assetId != null) {
                updateHouseArrearAmount(assetId);
            }

            return true;
        } else {
            throw new Exception("作废账单失败");
        }
    }

    /**
     * 批量收款
     */
    public int batchPayment(String[] billIds, Integer paymentType, String remark, SysUser currentUser) throws Exception {
        if (billIds == null || billIds.length == 0) {
            throw new Exception("请选择要收款的账单");
        }

        int successCount = 0;
        String currentTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date());

        // 验证所有账单是否属于同一个资产
        String firstAssetId = null;
        for (String billIdStr : billIds) {
            Long billId = Long.parseLong(billIdStr);
            Record bill = Db.findFirst("select asset_id, asset_name, pay_status, is_bad_bill from eh_charge_bill where id = ?", billId);

            if (bill == null) {
                throw new Exception("账单ID " + billId + " 不存在");
            }

            // 检查账单状态
            Integer payStatus = bill.getInt("pay_status");
            if (payStatus != null && payStatus > 0) {
                throw new Exception("账单ID " + billId + " 已经缴费，不能重复收款");
            }

            Integer isBadBill = bill.getInt("is_bad_bill");
            if (isBadBill != null && isBadBill == 1) {
                throw new Exception("账单ID " + billId + " 已作废，不能收款");
            }

            // 检查是否属于同一个资产
            String assetId = bill.getStr("asset_id");
            if (firstAssetId == null) {
                firstAssetId = assetId;
            } else if (!firstAssetId.equals(assetId)) {
                throw new Exception("批量收款只能选择同一个资产的账单");
            }
        }

        // 解析账单ID列表，优化批量处理
        List<Long> billIdList = new ArrayList<>();
        for (String billIdStr : billIds) {
            try {
                billIdList.add(Long.parseLong(billIdStr));
            } catch (NumberFormatException e) {
                logger.warn("账单ID格式错误，跳过：{}", billIdStr);
            }
        }

        if (billIdList.isEmpty()) {
            throw new Exception("没有有效的账单ID");
        }

        // 批量查询账单信息，避免N+1查询
        List<Record> billsToUpdate = getBillsForBatchPayment(billIdList);

        if (billsToUpdate.isEmpty()) {
            throw new Exception("没有可收款的账单");
        }

        // 批量更新账单状态
        String payTime = DateUtils.getTime();
        String remarkText = "账单于 " + currentTime + " 被 " + currentUser.getUserName() + " 批量收款" +
                           (StringUtils.isNotEmpty(remark) ? "，备注：" + remark : "");

        successCount = batchUpdateBillPaymentStatusInService(billsToUpdate, paymentType, payTime,
                                                           currentTime, currentUser.getUserName(), remarkText);

        // 批量更新房屋欠费金额
        updateHouseArrearAmountForBillsInService(billsToUpdate);

        if (successCount == 0) {
            throw new Exception("没有账单收款成功，请检查账单状态");
        }

        logger.info("批量收款完成，成功收款{}条账单，操作人：{}", successCount, currentUser.getUserName());
        return successCount;
    }



    /**
     * 按绑定和时间范围批量生成账单
     */
    public int batchGenerateBillsByBindingAndRange(Long bindingId, String startMonth, String endMonth, SysUser currentUser) throws Exception {
        if (bindingId == null || bindingId <= 0) {
            throw new Exception("绑定ID不能为空");
        }

        // 获取指定的收费绑定，包含房屋面积和计量信息
        Record binding = Db.findFirst(
                "select cb.*, cs.name as charge_standard_name, cs.charge_type, cs.price, cs.fixed_amount, " +
                "cs.count_type, cs.round_type, cs.unit, cs.period_type, cs.accounting_period_day, " +
                "hi.total_area, hi.area, hi.combina_name, " +
                "cci.price as count_price, cci.area_type, cci.area_name " +
                "from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "left join eh_house_info hi on cb.asset_id = hi.house_id " +
                "left join eh_charge_count_info cci on cs.id = cci.charge_standard_id " +
                "where cb.id = ? and cb.is_active = 1 and cs.is_active = 1", bindingId);

        if (binding == null) {
            throw new Exception("收费绑定不存在或已禁用");
        }

        // 解析时间范围
        Date startDate = DateUtils.parseDate(startMonth + "-01", DateUtils.YYYY_MM_DD);
        Date endDate = DateUtils.parseDate(endMonth + "-01", DateUtils.YYYY_MM_DD);

        // 计算月份差
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        int generateCount = 0;
        Calendar currentCal = Calendar.getInstance();
        currentCal.setTime(startDate);

        while (!currentCal.after(endCal)) {
            String monthStr = DateUtils.parseDateToStr("yyyy-MM", currentCal.getTime());

            // 检查该月份是否已有账单
            boolean exists = Db.queryLong(
                    "select count(*) from eh_charge_bill " +
                    "where charge_binding_id = ? and in_month = ?",
                    bindingId, monthStr) > 0;

            if (!exists) {
                try {
                    // 生成该月份的账单
                    Long billId = generateBillForMonth(binding, monthStr, currentUser);
                    if (billId != null) {
                        generateCount++;
                        logger.info("成功生成预收账单，绑定ID：{}，月份：{}，账单ID：{}", bindingId, monthStr, billId);
                    }
                } catch (Exception e) {
                    logger.error("生成预收账单失败，绑定ID：{}，月份：{}，错误：{}", bindingId, monthStr, e.getMessage());
                }
            } else {
                logger.debug("账单已存在，跳过生成，绑定ID：{}，月份：{}", bindingId, monthStr);
            }

            // 移动到下一个月
            currentCal.add(Calendar.MONTH, 1);
        }

        return generateCount;
    }

    /**
     * 按时间范围批量生成账单（社区级别）- 优化版本，支持大数据量分页处理
     */
    public int batchGenerateBillsByRange(String startMonth, String endMonth, SysUser currentUser) throws Exception {
        String communityId = currentUser.getCommunityId();
        final int BATCH_SIZE = 100; // 每批处理100个绑定

        logger.info("开始批量生成账单，社区ID：{}，时间范围：{} 到 {}", communityId, startMonth, endMonth);

        // 分页获取社区内所有启用的收费绑定
        int offset = 0;
        int totalGenerateCount = 0;
        int totalBindingCount = 0;

        while (true) {
            // 分页查询收费绑定
            List<Record> bindings = Db.find(
                "select cb.id from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "where cb.community_id = ? and cb.is_active = 1 and cs.is_active = 1 " +
                "limit ? offset ?",
                communityId, BATCH_SIZE, offset);

            if (bindings.isEmpty()) {
                break; // 没有更多绑定，退出循环
            }

            logger.debug("处理第{}批收费绑定，数量：{}", (offset / BATCH_SIZE + 1), bindings.size());

            // 处理当前批次的绑定
            for (Record binding : bindings) {
                try {
                    Long bindingId = binding.getLong("id");
                    int count = batchGenerateBillsByBindingAndRange(bindingId, startMonth, endMonth, currentUser);
                    totalGenerateCount += count;
                    totalBindingCount++;

                    if (count > 0) {
                        logger.debug("绑定ID：{}，生成账单数量：{}", bindingId, count);
                    }
                } catch (Exception e) {
                    logger.error("为绑定生成账单失败，绑定ID：{}，错误：{}", binding.getLong("id"), e.getMessage());
                }
            }

            offset += BATCH_SIZE;

            // 记录进度
            if (totalBindingCount % 500 == 0) {
                logger.info("批量生成账单进度：已处理{}个绑定，生成{}条账单", totalBindingCount, totalGenerateCount);
            }
        }

        logger.info("社区批量生成账单完成，社区ID：{}，时间范围：{} 到 {}，处理{}个绑定，共生成{}条账单",
                communityId, startMonth, endMonth, totalBindingCount, totalGenerateCount);
        return totalGenerateCount;
    }

    /**
     * 为指定月份生成账单
     */
    private Long generateBillForMonth(Record binding, String monthStr, SysUser currentUser) throws Exception {
        // 使用统一的账期计算服务
        Map<String, Object> billPeriod = billPeriodCalculatorService.calculateBillPeriod(binding, monthStr);

        if (billPeriod.isEmpty()) {
            logger.debug("指定月份{}不在收费绑定有效期内，跳过生成账单，绑定ID：{}", monthStr, binding.getLong("id"));
            return null; // 返回null表示跳过生成
        }

        // 添加调试日志
        logger.debug("计算账单金额，绑定ID：{}，资产类型：{}，计算方式：{}，单价：{}，固定金额：{}，面积：{}",
                binding.getLong("id"), binding.getInt("asset_type"), binding.getInt("count_type"),
                binding.getBigDecimal("price"), binding.getBigDecimal("fixed_amount"), binding.getBigDecimal("total_area"));

        BigDecimal billAmount = calculateBillAmount(binding, billPeriod);

        logger.debug("计算结果，绑定ID：{}，账单金额：{}元",
                binding.getLong("id"), billAmount);

        // 创建账单请求
        BillCreateRequest request = new BillCreateRequest();
        request.setCommunityId(binding.getStr("community_id"));
        request.setAssetType(binding.getInt("asset_type"));
        request.setAssetId(binding.getLong("asset_id"));
        request.setAssetName(binding.getStr("asset_name"));
        request.setChargeStandardId(binding.getLong("charge_standard_id"));
        request.setChargeStandardVersion(1);
        request.setChargeBindingId(binding.getLong("id"));
        request.setChargeItemName(binding.getStr("charge_standard_name"));
        request.setChargeItemType(binding.getInt("charge_type"));
        request.setStartTime((Integer) billPeriod.get("startTime"));
        request.setEndTime((Integer) billPeriod.get("endTime"));
        request.setInMonth(monthStr);
        request.setBillType(3); // 系统生成
        request.setBillAmount(billAmount);
        request.setCurrentUser(currentUser);

        // 创建账单记录
        Long billId = createBillRecord(request);
        // 更新收费绑定的下次账单时间（调用业务服务的方法）
        chargeBindingService.updateNextBillTime(binding.getLong("id"));
        return billId;
    }







    /**
     * 根据收费绑定生成账单
     */
    public Long generateBillByBinding(Long bindingId, SysUser currentUser) throws Exception {
        // 获取收费绑定信息，包含房屋面积和计量信息
        Record binding = Db.findFirst(
                "select cb.*, cs.name as standard_name, cs.charge_type, cs.price, cs.fixed_amount, " +
                "cs.count_type, cs.round_type, cs.unit, cs.period_type, " +
                "hi.total_area, hi.area, hi.combina_name, " +
                "cci.price as count_price, cci.area_type, cci.area_name " +
                "from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "left join eh_house_info hi on cb.asset_id = hi.house_id " +
                "left join eh_charge_count_info cci on cs.id = cci.charge_standard_id " +
                "where cb.id = ?", bindingId);

        if (binding == null) {
            throw new Exception("收费绑定不存在");
        }

        if (binding.getInt("is_active") != 1) {
            throw new Exception("收费绑定已停用");
        }

        // 计算账期
        Map<String, Object> billPeriod = calculateBillPeriod(binding);
        
        // 检查是否已存在相同账期的账单
        String inMonth = (String) billPeriod.get("inMonth");
        Record existingBill = Db.findFirst(
                "select id from eh_charge_bill where charge_binding_id = ? and in_month = ?",
                bindingId, inMonth);
        
        if (existingBill != null) {
            throw new Exception("该账期的账单已存在");
        }

        // 计算账单金额
        BigDecimal billAmount = calculateBillAmount(binding, billPeriod);

        // 创建账单请求
        BillCreateRequest request = new BillCreateRequest();
        request.setCommunityId(currentUser.getCommunityId());
        request.setAssetType(binding.getInt("asset_type"));
        request.setAssetId(binding.getLong("asset_id"));
        request.setAssetName(binding.getStr("asset_name"));
        request.setChargeStandardId(binding.getLong("charge_standard_id"));
        request.setChargeStandardVersion(binding.getInt("version"));
        request.setChargeBindingId(bindingId);
        request.setChargeItemName(binding.getStr("standard_name"));
        request.setChargeItemType(binding.getInt("charge_type"));
        request.setStartTime((Integer) billPeriod.get("startTime"));
        request.setEndTime((Integer) billPeriod.get("endTime"));
        request.setInMonth(inMonth);
        request.setBillType(3); // 系统生成
        request.setBillAmount(billAmount);
        request.setCurrentUser(currentUser);

        // 创建账单记录
        Long billId = createBillRecord(request);

        // 系统生成账单的特殊设置
        Db.update("update eh_charge_bill set create_by = 'SYSTEM' where id = ?", billId);

        logger.info("根据收费绑定生成账单成功，绑定ID：{}，账单ID：{}", bindingId, billId);
        return billId;
    }

    /**
     * 获取账单统计信息
     */
    public Map<String, Object> getBillStatistics(JSONObject params, String communityId) {
        Map<String, Object> stats = new HashMap<>();

        // 总账单数
        Long totalCount = Db.queryLong("select count(*) from eh_charge_bill where community_id = ?", communityId);
        stats.put("totalCount", totalCount);

        // 未缴费账单数
        Long unpaidCount = Db.queryLong("select count(*) from eh_charge_bill where community_id = ? and pay_status = 0", communityId);
        stats.put("unpaidCount", unpaidCount);

        // 已缴费账单数
        Long paidCount = Db.queryLong("select count(*) from eh_charge_bill where community_id = ? and pay_status = 1", communityId);
        stats.put("paidCount", paidCount);

        // 总应收金额
        Long totalAmount = Db.queryLong("select IFNULL(sum(amount), 0) from eh_charge_bill where community_id = ?", communityId);
        stats.put("totalAmount", totalAmount != null ? totalAmount / 100.0 : 0);

        // 已收金额
        Long paidAmount = Db.queryLong("select IFNULL(sum(amount), 0) from eh_charge_bill where community_id = ? and pay_status = 1", communityId);
        stats.put("paidAmount", paidAmount != null ? paidAmount / 100.0 : 0);

        // 未收金额
        Long unpaidAmount = Db.queryLong("select IFNULL(sum(amount), 0) from eh_charge_bill where community_id = ? and pay_status = 0", communityId);
        stats.put("unpaidAmount", unpaidAmount != null ? unpaidAmount / 100.0 : 0);

        return stats;
    }

    /**
     * 验证账单参数
     */
    private void validateBillParams(JSONObject params) throws Exception {
        if (params.getIntValue("assetType") <= 0) {
            throw new Exception("资产类型不能为空");
        }
        if (params.getLongValue("assetId") <= 0) {
            throw new Exception("资产ID不能为空");
        }
        if (StringUtils.isEmpty(params.getString("assetName"))) {
            throw new Exception("资产名称不能为空");
        }
        if (params.getLongValue("chargeStandardId") <= 0) {
            throw new Exception("收费标准ID不能为空");
        }
        if (StringUtils.isEmpty(params.getString("chargeItemName"))) {
            throw new Exception("收费项目名称不能为空");
        }
        if (StringUtils.isEmpty(params.getString("amount"))) {
            throw new Exception("金额不能为空");
        }
        if (params.getLongValue("startTime") <= 0) {
            throw new Exception("账期开始时间不能为空");
        }
        if (params.getLongValue("endTime") <= 0) {
            throw new Exception("账期结束时间不能为空");
        }
        if (StringUtils.isEmpty(params.getString("inMonth"))) {
            throw new Exception("账期月份不能为空");
        }
    }

    /**
     * 获取资产关联的用户信息
     */
    public List<Record> getAssetUsers(Integer assetType, Long assetId) {
        List<Record> users = new ArrayList<>();

        if (assetType == 1) { // 房屋
            users = Db.find(
                    "select o.owner_id as id, o.owner_name as name, o.mobile as phone " +
                    "from eh_house_owner_rel hor " +
                    "left join eh_owner o on hor.owner_id = o.owner_id " +
                    "where hor.house_id = ? and hor.check_status = 1",
                    assetId);
        } else if (assetType == 2) { // 车位
            users = Db.find(
                    "select o.owner_id as id, o.owner_name as name, o.mobile as phone " +
                    "from eh_parking_owner_rel por " +
                    "left join eh_owner o on por.owner_id = o.owner_id " +
                    "where por.parking_id = ? and por.check_status = 1",
                    assetId);
        } else if (assetType == 3) { // 车辆
            users = Db.find(
                    "select o.owner_id as id, o.owner_name as name, o.mobile as phone " +
                    "from eh_vehicle_owner_rel vor " +
                    "left join eh_owner o on vor.owner_id = o.owner_id " +
                    "where vor.vehicle_id = ? and vor.check_status = '1'",
                    assetId);
        }

        // 格式化用户信息
        for (Record user : users) {
            String name = user.getStr("name");
            String phone = user.getStr("phone");
            if (StringUtils.isNotEmpty(phone)) {
                user.set("name", name + "(" + phone + ")");
            }
        }

        return users;
    }

    /**
     * 获取资产关联的用户信息字符串格式
     * 返回格式：柴燕(15537110565)，柴燕(15537110565)
     */
    public String getAssetUsersString(Integer assetType, Long assetId) {
        List<Record> users = getAssetUsers(assetType, assetId);

        if (users == null || users.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < users.size(); i++) {
            Record user = users.get(i);
            String name = user.getStr("name"); // 这里的name已经包含了电话号码格式：姓名(电话)

            if (i > 0) {
                sb.append("，"); // 使用中文逗号分隔
            }
            sb.append(name);
        }

        return sb.toString();
    }

    /**
     * 重新计算账单的应收金额
     * bill_amount = amount + late_money_amount - discount_amount
     */
    public void recalculateBillAmount(Long billId) throws Exception {
        Record bill = Db.findFirst("select * from eh_charge_bill where id = ?", billId);
        if (bill == null) {
            throw new Exception("账单不存在");
        }

        BigDecimal amount = bill.getBigDecimal("amount") != null ? bill.getBigDecimal("amount") : BigDecimal.ZERO;
        BigDecimal discountAmount = bill.getBigDecimal("discount_amount") != null ? bill.getBigDecimal("discount_amount") : BigDecimal.ZERO;
        BigDecimal lateMoneyAmount = bill.getBigDecimal("late_money_amount") != null ? bill.getBigDecimal("late_money_amount") : BigDecimal.ZERO;

        // 计算最终应收金额：账单金额 + 违约金 - 优惠金额
        BigDecimal finalBillAmount = amount.add(lateMoneyAmount).subtract(discountAmount);

        // 更新数据库
        Db.update("update eh_charge_bill set bill_amount = ? where id = ?", finalBillAmount, billId);

        logger.info("重新计算账单金额，账单ID：{}，基础金额：{}，优惠金额：{}，违约金：{}，最终金额：{}",
                billId, amount, discountAmount, lateMoneyAmount, finalBillAmount);
    }

    /**
     * 批量重新计算账单金额 - 优化版本，支持大数据量分页处理
     */
    public int batchRecalculateBillAmount(String communityId) {
        final int PAGE_SIZE = 1000; // 每页处理1000条记录
        int offset = 0;
        int totalProcessed = 0;
        int successCount = 0;

        logger.info("开始批量重新计算账单金额，社区ID：{}", communityId);

        while (true) {
            // 分页查询账单ID
            List<Record> bills = Db.find(
                "select id from eh_charge_bill where community_id = ? limit ? offset ?",
                communityId, PAGE_SIZE, offset);

            if (bills.isEmpty()) {
                break; // 没有更多数据，退出循环
            }

            logger.debug("处理第{}页账单，数量：{}", (offset / PAGE_SIZE + 1), bills.size());

            // 处理当前批次
            for (Record bill : bills) {
                try {
                    recalculateBillAmount(bill.getLong("id"));
                    successCount++;
                } catch (Exception e) {
                    logger.error("重新计算账单金额失败，账单ID：{}，错误：{}", bill.getLong("id"), e.getMessage());
                }
                totalProcessed++;
            }

            offset += PAGE_SIZE;

            // 记录进度
            if (totalProcessed % 5000 == 0) {
                logger.info("批量重新计算账单金额进度：已处理{}条，成功{}条", totalProcessed, successCount);
            }
        }

        logger.info("批量重新计算账单金额完成，社区ID：{}，总处理{}条，成功{}条账单",
                   communityId, totalProcessed, successCount);
        return successCount;
    }

    /**
     * 计算账期
     */
    private Map<String, Object> calculateBillPeriod(Record binding) {
        return billPeriodCalculatorService.calculateBillPeriod(binding, null);
    }

    /**
     * 计算账单金额
     */
    private BigDecimal calculateBillAmount(Record binding, Map<String, Object> billPeriod) {
        BigDecimal amount = BigDecimal.ZERO;

        Integer assetType = binding.getInt("asset_type");
        Integer countType = binding.getInt("count_type");

        if (countType != null && countType == ChargeConstants.CountType.FIXED_AMOUNT) {
            // 固定金额
            BigDecimal fixedAmount = binding.getBigDecimal("fixed_amount");
            if (fixedAmount != null) {
                amount = fixedAmount;
            }
        } else if (assetType != null && assetType == ChargeConstants.AssetType.HOUSE) {
            // 只有房屋类型才基于面积计算
            BigDecimal unitPrice = binding.getBigDecimal("count_price"); // 从计量信息表获取单价
            if (unitPrice == null) {
                unitPrice = binding.getBigDecimal("price"); // 兼容处理
            }

            if (unitPrice != null) {
                // 根据面积类型获取计量数据
                BigDecimal measurement = BigDecimal.ZERO;
                Integer areaType = binding.getInt("area_type");

                if (areaType != null && areaType == ChargeConstants.AreaType.TOTAL_AREA) {
                    // 建筑面积
                    BigDecimal totalArea = binding.getBigDecimal("total_area");
                    if (totalArea != null) {
                        measurement = totalArea;
                    }
                } else if (areaType != null && areaType == ChargeConstants.AreaType.AREA) {
                    // 使用面积
                    BigDecimal area = binding.getBigDecimal("area");
                    if (area != null) {
                        measurement = area;
                    }
                } else {
                    // 默认使用建筑面积
                    BigDecimal totalArea = binding.getBigDecimal("total_area");
                    if (totalArea != null) {
                        measurement = totalArea;
                    }
                }

                // 计算金额 = 单价 * 面积
                if (measurement.compareTo(BigDecimal.ZERO) > 0) {
                    amount = unitPrice.multiply(measurement);
                }
            }
        } else {
            // 非房屋类型（车位、车辆等）使用固定金额
            BigDecimal fixedAmount = binding.getBigDecimal("fixed_amount");
            if (fixedAmount != null) {
                amount = fixedAmount;
            } else {
                // 如果没有设置固定金额，使用默认金额
                amount = new BigDecimal("100.00");
            }
        }

        // 如果金额仍为0，设置默认金额
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            amount = new BigDecimal(ChargeConstants.DefaultValue.DEFAULT_BILL_AMOUNT)
                    .divide(new BigDecimal(ChargeConstants.DefaultValue.FEN_TO_YUAN)); // 默认100元
        }

        // 应用取整规则
        Integer roundType = binding.getInt("round_type");
        if (roundType != null) {
            switch (roundType) {
                case ChargeConstants.RoundType.ROUND_HALF_UP: // 四舍五入
                    amount = amount.setScale(2, RoundingMode.HALF_UP);
                    break;
                case ChargeConstants.RoundType.ROUND_DOWN: // 抹零
                    amount = amount.setScale(0, RoundingMode.DOWN);
                    break;
                case ChargeConstants.RoundType.ROUND_UP: // 向上取整
                    amount = amount.setScale(0, RoundingMode.UP);
                    break;
                default:
                    amount = amount.setScale(2, RoundingMode.HALF_UP);
                    break;
            }
        } else {
            amount = amount.setScale(2, RoundingMode.HALF_UP);
        }

        // 处理不足月情况
        BigDecimal incompleteMonthRatio = billPeriodCalculatorService.calculateIncompleteMonthRatio(billPeriod);
        if (incompleteMonthRatio.compareTo(BigDecimal.ONE) != 0) {
            amount = amount.multiply(incompleteMonthRatio);
            // 重新应用取整规则
            if (roundType != null) {
                switch (roundType) {
                    case ChargeConstants.RoundType.ROUND_HALF_UP:
                        amount = amount.setScale(2, RoundingMode.HALF_UP);
                        break;
                    case ChargeConstants.RoundType.ROUND_DOWN:
                        amount = amount.setScale(0, RoundingMode.DOWN);
                        break;
                    case ChargeConstants.RoundType.ROUND_UP:
                        amount = amount.setScale(0, RoundingMode.UP);
                        break;
                    default:
                        amount = amount.setScale(2, RoundingMode.HALF_UP);
                        break;
                }
            } else {
                amount = amount.setScale(2, RoundingMode.HALF_UP);
            }

            logger.debug("应用不足月比例，绑定ID：{}，原金额：{}，比例：{}，最终金额：{}",
                    binding.getLong("id"), amount.divide(incompleteMonthRatio, 2, RoundingMode.HALF_UP),
                    incompleteMonthRatio, amount);
        }

        return amount;
    }

    /**
     * 更新房屋欠费金额
     * 根据未缴费账单重新计算房屋的欠费总额
     */
    public void updateHouseArrearAmount(Long houseId) {
        if (houseId == null) {
            return;
        }

        // 计算房屋的未缴费账单总额
        BigDecimal arrearAmount = Db.queryBigDecimal(
            "select IFNULL(sum(bill_amount), 0.00) from eh_charge_bill " +
            "where asset_type = 1 and asset_id = ? and pay_status = 0 and is_bad_bill = 0",
            houseId);

        if (arrearAmount == null) {
            arrearAmount = BigDecimal.ZERO;
        }

        // 更新房屋表的欠费金额
        Db.update("update eh_house_info set arrear_amount = ? where house_id = ?",
                arrearAmount, houseId);

        logger.debug("更新房屋欠费金额，房屋ID：{}，欠费金额：{}", houseId, arrearAmount);
    }

    /**
     * 计算房屋欠费金额（不更新数据库）
     */
    public BigDecimal calculateHouseArrearAmount(Long houseId) {
        if (houseId == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal arrearAmount = Db.queryBigDecimal(
            "select IFNULL(sum(bill_amount), 0.00) from eh_charge_bill " +
            "where asset_type = 1 and asset_id = ? and pay_status = 0 and is_bad_bill = 0",
            houseId);

        return arrearAmount != null ? arrearAmount : BigDecimal.ZERO;
    }

    /**
     * 批量更新所有房屋的欠费金额
     */
    public int updateAllHouseArrearAmount(String communityId) {
        String sql = "UPDATE eh_house_info h " +
                    "SET arrear_amount = (" +
                    "    SELECT COALESCE(SUM(cb.bill_amount), 0.00) " +
                    "    FROM eh_charge_bill cb " +
                    "    WHERE cb.asset_type = 1 " +
                    "    AND cb.asset_id = h.house_id " +
                    "    AND cb.pay_status = 0 " +
                    "    AND cb.is_bad_bill = 0" +
                    ") WHERE h.community_id = ?";

        int updateCount = Db.update(sql, communityId);
        logger.info("批量更新房屋欠费金额完成，小区ID：{}，更新数量：{}", communityId, updateCount);
        return updateCount;
    }

    /**
     * 批量查询账单信息用于批量收款 - 优化版本，支持大数据量分页处理
     */
    private List<Record> getBillsForBatchPayment(List<Long> billIds) {
        if (billIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 分页处理大数据量，避免IN查询限制
        final int BATCH_SIZE = 500; // 每批处理500个ID
        List<Record> allResults = new ArrayList<>();

        for (int i = 0; i < billIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, billIds.size());
            List<Long> batchIds = billIds.subList(i, endIndex);

            // 构建当前批次的IN查询条件
            String inClause = batchIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            List<Record> batchResults = Db.find(
                "SELECT id, asset_type, asset_id, pay_status FROM eh_charge_bill " +
                "WHERE id IN (" + inClause + ") AND pay_status = 0"
            );

            allResults.addAll(batchResults);
        }

        return allResults;
    }

    /**
     * 批量更新账单支付状态 - 优化版本，支持大数据量分页处理
     */
    private int batchUpdateBillPaymentStatusInService(List<Record> bills, Integer paymentType,
                                                     String payTime, String currentTime,
                                                     String userName, String remarkText) {
        if (bills.isEmpty()) {
            return 0;
        }

        // 提取账单ID列表
        List<Long> billIds = bills.stream()
            .map(bill -> bill.getLong("id"))
            .collect(Collectors.toList());

        // 分页处理大数据量更新
        final int BATCH_SIZE = 500; // 每批处理500个ID
        int totalUpdateCount = 0;

        for (int i = 0; i < billIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, billIds.size());
            List<Long> batchIds = billIds.subList(i, endIndex);

            // 构建当前批次的IN查询条件
            String inClause = batchIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            // 执行当前批次的批量更新
            int batchUpdateCount = Db.update(
                "UPDATE eh_charge_bill SET " +
                "pay_status = 1, " +
                "pay_type = ?, " +
                "pay_time = ?, " +
                "update_time = ?, " +
                "update_by = ?, " +
                "remark = CONCAT(IFNULL(remark, ''), '\\n', ?) " +
                "WHERE id IN (" + inClause + ") AND pay_status = 0",
                paymentType, payTime, currentTime, userName, remarkText
            );

            totalUpdateCount += batchUpdateCount;

            // 记录批次处理日志
            logger.debug("批量更新账单支付状态，批次：{}/{}, 更新数量：{}",
                        (i / BATCH_SIZE + 1), (billIds.size() + BATCH_SIZE - 1) / BATCH_SIZE, batchUpdateCount);
        }

        return totalUpdateCount;
    }

    /**
     * 批量更新房屋欠费金额
     */
    private void updateHouseArrearAmountForBillsInService(List<Record> bills) {
        // 提取房屋ID列表
        List<Long> houseIds = bills.stream()
            .filter(bill -> bill.getInt("asset_type") == 1) // 只处理房屋类型
            .map(bill -> bill.getLong("asset_id"))
            .distinct()
            .collect(Collectors.toList());

        if (!houseIds.isEmpty()) {
            // 批量更新房屋欠费金额
            for (Long houseId : houseIds) {
                updateHouseArrearAmount(houseId);
            }
            logger.info("批量更新房屋欠费金额完成，更新房屋数量：{}", houseIds.size());
        }
    }

}
