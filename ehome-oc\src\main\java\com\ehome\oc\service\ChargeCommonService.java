package com.ehome.oc.service;

import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.common.utils.DateUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收费模块公共服务类
 * 用于抽取各个Controller中的重复代码
 */
@Service
public class ChargeCommonService {

    /**
     * 设置创建和更新信息
     */
    public void setCreateAndUpdateInfo(Record record, SysUser user) {
        String now = DateUtils.getTime();
        String loginName = user.getLoginName();
        record.set("community_id", user.getCommunityId());
        record.set("created_at", now);
        record.set("updated_at", now);
        record.set("created_by", loginName);
        record.set("updated_by", loginName);
        record.set("is_deleted", false);
        record.set("is_active", 1);
    }

    /**
     * 设置更新信息
     */
    public void setUpdateInfo(Record record, SysUser user) {
        record.set("updated_at", DateUtils.getTime());
        record.set("updated_by", user.getLoginName());
    }

    /**
     * 设置创建和更新信息（兼容旧版本方法名）
     */
    public void setCreateAndUpdateInfo(Record record, String loginName, String communityId) {
        String now = DateUtils.getTime();
        record.set("community_id", communityId);
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
        record.set("is_active", 1);
    }

    /**
     * 设置更新信息（兼容旧版本方法名）
     */
    public void setUpdateInfo(Record record, String loginName) {
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", loginName);
    }

    /**
     * 获取收费标准列表
     */
    public List<Record> getChargeStandards(String communityId) {
        return Db.find(
                "select id, name, charge_type, period_type, count_type " +
                "from eh_charge_standard where is_deleted = 0 and is_active = 1 and is_current = 1 and community_id = ? " +
                "order by created_at desc", communityId);
    }

    /**
     * 更新收费标准的关联资产数
     */
    public void updateChargeStandardAssetCount(Long chargeStandardId) {
        if (chargeStandardId == null || chargeStandardId <= 0) {
            return;
        }

        // 统计该收费标准关联的资产数量
        Long count = Db.queryLong(
                "select count(*) from eh_charge_binding where charge_standard_id = ? and is_active = 1",
                chargeStandardId);

        // 更新收费标准表的关联资产数
        Db.update(
                "update eh_charge_standard set related_asset_count = ? where id = ?",
                count, chargeStandardId);
    }

    /**
     * 将Record列表转换为Map列表
     */
    public java.util.List<java.util.Map<String, Object>> recordToMap(List<Record> records) {
        java.util.List<java.util.Map<String, Object>> result = new java.util.ArrayList<>();
        if (records != null) {
            for (Record record : records) {
                result.add(record.toMap());
            }
        }
        return result;
    }
}
