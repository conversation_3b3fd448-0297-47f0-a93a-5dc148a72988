<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费绑定管理')" />
    <style>
        .select-list li p, .select-list li label:not(.radio-box) {
            width: 135px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>资产类型：</label>
                            <select name="assetType" th:with="type=${@dict.getType('asset_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>资产名称：</label>
                            <input type="text" name="assetName" placeholder="请输入资产名称"/>
                        </li>
                        <li>
                            <label>绑定收费标准：</label>
                            <select name="chargeStandardId" id="chargeStandardSelect">
                                <option value="">所有</option>
                            </select>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="isActive">
                                <option value="">所有</option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </li>
                        <li>
                            <label>计费开始日期：</label>
                            <input type="text" class="time-input" id="chargeStartTime" placeholder="开始日期" name="chargeBeginTime" style="width: 95px;"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="chargeEndTime" placeholder="结束日期" name="chargeEndTime" style="width: 95px;"/>
                        </li>
                        <li>
                            <label>下次生成账单日期：</label>
                            <input type="text" class="time-input" id="nextBillStartTime" placeholder="开始日期" name="nextBillBeginTime" style="width: 95px;"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="nextBillEndTime" placeholder="结束日期" name="nextBillEndTime" style="width: 95px;"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="oc:charge:binding:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="oc:charge:binding:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="oc:charge:binding:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-info" onclick="refreshAllBillDates()" shiro:hasPermission="oc:charge:binding:edit">
                <i class="fa fa-refresh"></i> 刷新账单日期
            </a>
            <a class="btn btn-warning single disabled" onclick="generatePreBills()" shiro:hasPermission="oc:charge:binding:edit">
                <i class="fa fa-calendar"></i> 生成预收账单
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<!-- 生成预收账单弹窗 -->
<div class="modal fade" id="preBillModal" tabindex="-1" role="dialog" aria-labelledby="preBillModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="preBillModalLabel">生成预收账单</h4>
            </div>
            <div class="modal-body">
                <form id="preBillForm">
                    <div class="form-group">
                        <label>已选择资产：</label>
                        <div id="selectedAssetName" class="form-control-static"></div>
                    </div>
                    <div class="form-group">
                        <label>账期范围：</label>
                        <div class="row">
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="startMonth" placeholder="开始月份" readonly>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="endMonth" placeholder="结束月份" readonly>
                            </div>
                        </div>
                        <small class="help-block">格式：YYYY-MM，如已有账单则跳过，不会重复生成</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGeneratePreBills()">确认生成</button>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('oc:charge:binding:edit')}]];
    var removeFlag = [[${@permission.hasPermi('oc:charge:binding:remove')}]];
    var prefix = ctx + "oc/charge/setting/binding";

    $(function() {
        // 先加载收费标准下拉选项，然后初始化表格
        loadChargeStandards(function() {
            initTable();
            // 如果URL中有chargeStandardId参数，自动设置筛选条件
            var urlParams = new URLSearchParams(window.location.search);
            var chargeStandardId = urlParams.get('chargeStandardId');
            if (chargeStandardId) {
                $("select[name='chargeStandardId']").val(chargeStandardId);
                $.table.search();
            }
        });

        // 初始化时间选择器
        laydate.render({
            elem: '#chargeStartTime'
        });
        laydate.render({
            elem: '#chargeEndTime'
        });
        laydate.render({
            elem: '#nextBillStartTime'
        });
        laydate.render({
            elem: '#nextBillEndTime'
        });
    });

    function initTable() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "收费绑定",
            columns: [{
                checkbox: true
            },
            {
                field: 'id',
                title: '绑定ID',
                visible: false
            },
            {
                field: 'asset_type',
                title: '资产类型',
                formatter: function(value, row, index) {
                    return getText(value, 'assetType');
                }
            },
            {
                field: 'asset_name',
                title: '资产名称'
            },
            {
                field: 'charge_standard_id',
                title: '绑定收费标准',
                formatter: function(value, row, index) {
                    var standardName = getText(value, 'chargeStandardId');
                    if (standardName && value) {
                        return standardName + ' <i class="fa fa-question-circle" style="color: #999; cursor: pointer;" onmouseover="showChargeStandardTip(this, ' + value + ')" onmouseout="layer.closeAll(\'tips\')"></i>';
                    }
                    return standardName;
                }
            },
            {
                field: 'chargePeriod',
                title: '计费开始日期',
                formatter: function(value, row, index) {
                    var startTime = row.startTimeStr || '';
                    var endTime = row.endTimeStr || '无结束日期';
                    return startTime + ' - ' + endTime;
                }
            },
            {
                field: 'nextBillTimeStr',
                title: '下次生成账单日期'
            },
            {
                field: 'period_num',
                title: '收费周期',
                formatter: function(value, row, index) {
                    return value + '个月';
                }
            },
            {
                field: 'natural_period',
                title: '自然月账单',
                formatter: function(value, row, index) {
                    return value == 1 ? '<span class="badge badge-success">是</span>' : '<span class="badge badge-secondary">否</span>';
                }
            },
            {
                field: 'is_active',
                title: '状态',
                formatter: function(value, row, index) {
                    console.log("is_active:"+value);
                    return value == '1' ? '<span class="badge badge-success">启用</span>' : '<span class="badge badge-danger">禁用</span>';
                }
            },
            {
                field: 'create_time',
                title: '创建时间'
            },
            {
                title: '操作',
                align: 'center',
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    actions.push('<a class="btn btn-warning btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="unbindAsset(\'' + row.id + '\')"><i class="fa fa-unlink"></i>解绑</a> ');
                    actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    }

    function loadChargeStandards(callback) {
        $.post(prefix + "/getChargeStandards", {}, function(result) {
            if (result.code == 0) {
                var select = $("#chargeStandardSelect");
                select.empty();
                select.append('<option value="">所有</option>');
                $.each(result.data, function(index, item) {
                    select.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
                if (callback) callback();
            }
        });
    }

    // 显示收费标准详细信息提示
    function showChargeStandardTip(element, chargeStandardId) {
        $.post(prefix + "/getChargeStandardDetail", {id: chargeStandardId}, function(result) {
            if (result.code == 0) {
                var data = result.data;
                var tipContent = '<div style="text-align: left; line-height: 1.6;">';
                tipContent += '<strong>收费ID：</strong>' + (data.id || '') + '<br>';
                tipContent += '<strong>类型：</strong>' + getChargeTypeText(data.charge_type) + '<br>';
                tipContent += '<strong>计算精度：</strong>' + getRoundTypeText(data.round_type, data.unit) + '<br>';
                tipContent += '<strong>收费方式：</strong>' + getPeriodTypeText(data.period_type, data.incomplete_month_handling) + '<br>';
                tipContent += '<strong>金额计算方式：</strong>' + getCountTypeText(data.count_type, data) + '<br>';
                if (data.count_type == 100 && data.price) {
                    tipContent += '<strong>单价：</strong>' + data.price + '元<br>';
                }
                if (data.fixed_amount) {
                    tipContent += '<strong>固定金额：</strong>' + data.fixed_amount + '元<br>';
                }
                tipContent += '<strong>账单生成日期：</strong>' + getAccountingPeriodText(data.accounting_period_type, data.accounting_period_day) + '<br>';
                tipContent += '<strong>违约金：</strong>' + getLateFeeText(data.late_money_type) + '<br>';
                if (data.remark) {
                    tipContent += '<strong>备注：</strong>' + data.remark;
                }
                tipContent += '</div>';

                layer.tips(tipContent, element, {
                    tips: [1, '#3595CC'],
                    time: 0,
                    area: ['300px', 'auto']
                });
            }
        });
    }

    // 获取收费类型文本
    function getChargeTypeText(type) {
        switch(type) {
            case 1: return '周期性收费';
            case 2: return '走表收费';
            case 3: return '临时性收费';
            default: return '未知';
        }
    }

    // 获取计算精度文本
    function getRoundTypeText(roundType, unit) {
        var unitText = '';
        switch(unit) {
            case 1: unitText = '元（不保留小数）'; break;
            case 2: unitText = '角（保留一位小数）'; break;
            case 3: unitText = '分（保留两位小数）'; break;
            default: unitText = '元';
        }

        var roundText = '';
        switch(roundType) {
            case 0: roundText = '四舍五入'; break;
            case 1: roundText = '抹零'; break;
            case 2: roundText = '向上取整'; break;
            default: roundText = '四舍五入';
        }

        return unitText + '，' + roundText;
    }

    // 获取收费方式文本
    function getPeriodTypeText(type, incompleteHandling) {
        var baseText = '';
        switch(type) {
            case 101: baseText = '按月收费'; break;
            case 102: baseText = '按季度收费'; break;
            case 103: baseText = '按年收费'; break;
            default: baseText = '未知';
        }

        if (incompleteHandling) {
            var handlingText = '';
            switch(incompleteHandling) {
                case 1: handlingText = '不足一月:按天收费'; break;
                case 2: handlingText = '不足一月:按月收费'; break;
                case 3: handlingText = '不足一月:不收费'; break;
                default: handlingText = '';
            }
            if (handlingText) {
                baseText += ',' + handlingText;
            }
        }

        return baseText;
    }

    // 获取计算方式文本
    function getCountTypeText(type, data) {
        switch(type) {
            case 100:
                var areaTypeText = '';
                var countInfo = {};
                try {
                    if (data.count_info) {
                        countInfo = JSON.parse(data.count_info);
                    }
                } catch(e) {
                    console.log('解析count_info失败:', e);
                }

                var areaType = countInfo.area_type || data.area_type || 1;
                switch(parseInt(areaType)) {
                    case 1: areaTypeText = '建筑面积'; break;
                    case 2: areaTypeText = '套内面积'; break;
                    case 3: areaTypeText = '公摊面积'; break;
                    default: areaTypeText = '面积';
                }

                var price = countInfo.price || data.price || '0';
                return '单价*计量方式:\n' + areaTypeText + '*' + price;
            case 200:
                return '固定金额:' + (data.fixed_amount || '0') + '元';
            default:
                return '未知';
        }
    }

    // 获取账期文本
    function getAccountingPeriodText(type, day) {
        if (type == 1) {
            return '账期本月' + (day || 1) + '号';
        } else if (type == 2) {
            return '账期下月' + (day || 1) + '号';
        }
        return '未设置';
    }

    // 获取违约金文本
    function getLateFeeText(type) {
        return type == 0 ? '无' : '有';
    }

    // 解绑资产
    function unbindAsset(id) {
        $.modal.confirm("确认要解绑该资产的收费标准吗？", function() {
            $.post(prefix + "/unbind", {id: id}, function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess("解绑成功");
                    $.table.refresh();
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        });
    }

    // 刷新所有收费绑定的下次账单日期
    function refreshAllBillDates() {
        $.modal.confirm("确认要刷新所有收费绑定的下次账单日期吗？", function() {
            $.post(prefix + "/refreshAllBillDates", {}, function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                    $.table.refresh();
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        });
    }

    // 生成预收账单
    function generatePreBills() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var selectedRow = rows[0];
        $('#selectedAssetName').text(selectedRow.asset_name);

        // 初始化日期选择器
        layui.laydate.render({
            elem: '#startMonth',
            type: 'month',
            format: 'yyyy-MM'
        });
        layui.laydate.render({
            elem: '#endMonth',
            type: 'month',
            format: 'yyyy-MM'
        });

        // 显示弹窗
        $('#preBillModal').modal('show');
    }

    // 确认生成预收账单
    function confirmGeneratePreBills() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length != 1) {
            $.modal.alertWarning("请重新选择记录");
            return;
        }

        var startMonth = $('#startMonth').val();
        var endMonth = $('#endMonth').val();

        if (!startMonth || !endMonth) {
            $.modal.alertWarning("请选择账期范围");
            return;
        }

        if (startMonth > endMonth) {
            $.modal.alertWarning("开始月份不能大于结束月份");
            return;
        }

        var selectedRow = rows[0];
        var data = {
            bindingId: selectedRow.id,
            assetId: selectedRow.asset_id,
            assetName: selectedRow.asset_name,
            startMonth: startMonth,
            endMonth: endMonth
        };

        $.modal.loading("正在生成预收账单，请稍候...");

        $.post(prefix + "/generatePreBills", data, function(result) {
            $.modal.closeLoading();
            if (result.code == 0) {
                $.modal.msgSuccess(result.msg);
                $('#preBillModal').modal('hide');
                $.table.refresh();
            } else {
                $.modal.msgError(result.msg);
            }
        }).fail(function() {
            $.modal.closeLoading();
            $.modal.msgError("生成预收账单失败");
        });
    }


</script>
</body>
</html>
